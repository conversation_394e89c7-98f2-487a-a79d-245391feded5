<template>
  <div class="user-list-page">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加用户
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="userList"
      border
      style="width: 100%"
    >
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="roleName" label="角色" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="lastLoginTime" label="最后登录" width="180" />
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="warning" link @click="handleResetPassword(row)">重置密码</el-button>
          <el-button 
            :type="row.status === 'ACTIVE' ? 'warning' : 'success'" 
            link 
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 'ACTIVE' ? '停用' : '启用' }}
          </el-button>
          <el-popconfirm
            title="确定要删除这个用户吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <UserForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordDialogVisible"
      title="重置密码"
      width="40%"
      destroy-on-close
    >
      <el-form
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmResetPassword">确认重置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getUserList, deleteUser, toggleUserStatus, resetUserPassword } from '@/api/modules/user';
import { getAllRoles } from '@/api/modules/role';
import type { User, Role, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import UserForm from './UserForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '用户名', prop: 'username', type: 'input' },
    { 
      label: '角色', 
      prop: 'roleId', 
      type: 'select',
      options: [] as { label: string; value: number }[]
    },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '正常', value: 'ACTIVE' },
        { label: '停用', value: 'INACTIVE' }
      ]
    }
  ]
});

// 数据列表
const userList = ref<User[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加用户',
    'edit': '编辑用户',
    'view': '查看用户详情'
  };
  return titleMap[formMode.value];
});

// 重置密码对话框相关
const resetPasswordDialogVisible = ref(false);
const resetPasswordFormRef = ref<FormInstance>();
const resetPasswordForm = reactive({
  userId: 0,
  newPassword: '',
  confirmPassword: ''
});
const resetPasswordRules = reactive<FormRules>({
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetPasswordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 获取角色列表
const fetchRoles = async () => {
  try {
    const roleList = await getAllRoles();
    searchConfig.fields[1].options = roleList.map(item => ({
      label: item.roleName,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true;
  try {
    const result = await getUserList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    userList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchUserList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchUserList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchUserList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: User) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: User) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteUser(id);
    ElMessage.success('删除成功');
    fetchUserList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理启用/停用
const handleToggleStatus = async (row: User) => {
  const newStatus = row.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  const action = newStatus === 'ACTIVE' ? '启用' : '停用';
  
  try {
    await ElMessageBox.confirm(`确定要${action}这个用户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await toggleUserStatus(row.id!, newStatus);
    ElMessage.success(`${action}成功`);
    fetchUserList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || `${action}失败`);
    }
  }
};

// 处理重置密码
const handleResetPassword = (row: User) => {
  resetPasswordForm.userId = row.id!;
  resetPasswordForm.newPassword = '';
  resetPasswordForm.confirmPassword = '';
  resetPasswordDialogVisible.value = true;
};

// 确认重置密码
const handleConfirmResetPassword = async () => {
  if (!resetPasswordFormRef.value) return;
  
  await resetPasswordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await resetUserPassword(resetPasswordForm.userId, resetPasswordForm.newPassword);
        ElMessage.success('密码重置成功');
        resetPasswordDialogVisible.value = false;
      } catch (error: any) {
        ElMessage.error(error.msg || '密码重置失败');
      }
    }
  });
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchUserList();
};

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'ACTIVE': 'success',
    'INACTIVE': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'ACTIVE': '正常',
    'INACTIVE': '停用'
  };
  return statusMap[status] || status;
};

// 页面加载时获取数据
onMounted(() => {
  fetchRoles();
  fetchUserList();
});
</script>

<style scoped>
.user-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>