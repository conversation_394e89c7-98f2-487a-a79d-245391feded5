package com.example.medicine.controller;

import com.example.medicine.entity.Purchase;
import com.example.medicine.service.PurchaseService;
import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/purchase")
public class PurchaseController {

    @Autowired
    private PurchaseService purchaseService;

    @GetMapping("/list")
    public Result<PageResult<Purchase>> list(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String keyword) {
        try {
            List<Purchase> allPurchases = purchaseService.findAllWithDetails();

            // 简单的分页逻辑
            int total = allPurchases.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);

            List<Purchase> records = start < total ? allPurchases.subList(start, end) : List.of();

            PageResult<Purchase> pageResult = new PageResult<>();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<Purchase> get(@PathVariable Long id) {
        try {
            Purchase purchase = purchaseService.findByIdWithDetails(id);
            if (purchase == null) {
                return Result.error("采购记录不存在");
            }
            return Result.success(purchase);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/add")
    public Purchase add(@RequestBody Purchase purchase) {
        return purchaseService.save(purchase);
    }

    @PutMapping("/update/{id}")
    public Purchase update(@PathVariable Long id, @RequestBody Purchase purchase) {
        purchase.setId(id);
        return purchaseService.save(purchase);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        purchaseService.deleteById(id);
    }

    @PostMapping("/complete/{id}")
    public Result<Purchase> complete(@PathVariable Long id) {
        try {
            Purchase purchase = purchaseService.completePurchase(id);
            return Result.success(purchase);
        } catch (Exception e) {
            return Result.error("完成采购失败: " + e.getMessage());
        }
    }
}