<template>
  <div class="role-list-page">
    <div class="page-header">
      <h2>角色管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加角色
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="roleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="roleName" label="角色名称" width="150" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="permissions" label="权限" min-width="300">
        <template #default="{ row }">
          <el-tag
            v-for="permission in (row.permissions || [])"
            :key="permission"
            size="small"
            style="margin-right: 5px; margin-bottom: 5px;"
          >
            {{ getPermissionText(permission) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="warning" link @click="handleAssignPermissions(row)">分配权限</el-button>
          <el-popconfirm
            title="确定要删除这个角色吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 角色表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <RoleForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 分配权限对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="60%"
      destroy-on-close
    >
      <el-form label-width="100px">
        <el-form-item label="角色名称">
          <el-input :value="selectedRole?.roleName" readonly />
        </el-form-item>
        <el-form-item label="权限设置">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            :props="{ children: 'children', label: 'label' }"
            show-checkbox
            node-key="value"
            :default-checked-keys="selectedPermissions"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAssignPermissions">确认分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { ElTree } from 'element-plus';
import { getRoleList, deleteRole, assignPermissions, getRolePermissions, getAllPermissions } from '@/api/modules/role';
import type { Role, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import RoleForm from './RoleForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '角色名称', prop: 'roleName', type: 'input' },
    { label: '描述', prop: 'description', type: 'input' }
  ]
});

// 数据列表
const roleList = ref<Role[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加角色',
    'edit': '编辑角色',
    'view': '查看角色详情'
  };
  return titleMap[formMode.value];
});

// 权限分配对话框相关
const permissionDialogVisible = ref(false);
const permissionTreeRef = ref<InstanceType<typeof ElTree>>();
const selectedRole = ref<Role | null>(null);
const selectedPermissions = ref<string[]>([]);
const permissionTreeData = ref([
  {
    label: '药品管理',
    value: 'medicine',
    children: [
      { label: '查看药品', value: 'medicine:view' },
      { label: '添加药品', value: 'medicine:add' },
      { label: '编辑药品', value: 'medicine:edit' },
      { label: '删除药品', value: 'medicine:delete' }
    ]
  },
  {
    label: '库存管理',
    value: 'inventory',
    children: [
      { label: '查看库存', value: 'inventory:view' },
      { label: '入库操作', value: 'inventory:in' },
      { label: '出库操作', value: 'inventory:out' },
      { label: '库存调整', value: 'inventory:adjust' }
    ]
  },
  {
    label: '销售管理',
    value: 'sale',
    children: [
      { label: '查看销售', value: 'sale:view' },
      { label: '添加销售', value: 'sale:add' },
      { label: '编辑销售', value: 'sale:edit' },
      { label: '删除销售', value: 'sale:delete' },
      { label: '销售退货', value: 'sale:refund' }
    ]
  },
  {
    label: '采购管理',
    value: 'purchase',
    children: [
      { label: '查看采购', value: 'purchase:view' },
      { label: '添加采购', value: 'purchase:add' },
      { label: '编辑采购', value: 'purchase:edit' },
      { label: '删除采购', value: 'purchase:delete' },
      { label: '采购审批', value: 'purchase:approve' }
    ]
  },
  {
    label: '供应商管理',
    value: 'supplier',
    children: [
      { label: '查看供应商', value: 'supplier:view' },
      { label: '添加供应商', value: 'supplier:add' },
      { label: '编辑供应商', value: 'supplier:edit' },
      { label: '删除供应商', value: 'supplier:delete' }
    ]
  },
  {
    label: '客户管理',
    value: 'customer',
    children: [
      { label: '查看客户', value: 'customer:view' },
      { label: '添加客户', value: 'customer:add' },
      { label: '编辑客户', value: 'customer:edit' },
      { label: '删除客户', value: 'customer:delete' }
    ]
  },
  {
    label: '用户管理',
    value: 'user',
    children: [
      { label: '查看用户', value: 'user:view' },
      { label: '添加用户', value: 'user:add' },
      { label: '编辑用户', value: 'user:edit' },
      { label: '删除用户', value: 'user:delete' },
      { label: '重置密码', value: 'user:reset-password' }
    ]
  },
  {
    label: '角色管理',
    value: 'role',
    children: [
      { label: '查看角色', value: 'role:view' },
      { label: '添加角色', value: 'role:add' },
      { label: '编辑角色', value: 'role:edit' },
      { label: '删除角色', value: 'role:delete' },
      { label: '分配权限', value: 'role:assign-permissions' }
    ]
  },
  {
    label: '系统管理',
    value: 'system',
    children: [
      { label: '系统设置', value: 'system:settings' },
      { label: '操作日志', value: 'system:logs' },
      { label: '数据统计', value: 'system:statistics' }
    ]
  }
]);

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const result = await getRoleList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    roleList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchRoleList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchRoleList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchRoleList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Role) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Role) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteRole(id);
    ElMessage.success('删除成功');
    fetchRoleList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理分配权限
const handleAssignPermissions = async (row: Role) => {
  selectedRole.value = row;
  try {
    const permissions = await getRolePermissions(row.id!);
    selectedPermissions.value = permissions;
    permissionDialogVisible.value = true;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取角色权限失败');
  }
};

// 确认分配权限
const handleConfirmAssignPermissions = async () => {
  if (!selectedRole.value || !permissionTreeRef.value) return;
  
  try {
    const checkedKeys = permissionTreeRef.value.getCheckedKeys() as string[];
    const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys() as string[];
    const allPermissions = [...checkedKeys, ...halfCheckedKeys];
    
    await assignPermissions(selectedRole.value.id!, allPermissions);
    ElMessage.success('权限分配成功');
    permissionDialogVisible.value = false;
    fetchRoleList();
  } catch (error: any) {
    ElMessage.error(error.msg || '权限分配失败');
  }
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchRoleList();
};

// 获取权限文本
const getPermissionText = (permission: string) => {
  const findPermissionText = (nodes: any[]): string => {
    for (const node of nodes) {
      if (node.value === permission) {
        return node.label;
      }
      if (node.children) {
        const result = findPermissionText(node.children);
        if (result) return result;
      }
    }
    return permission;
  };
  
  return findPermissionText(permissionTreeData.value);
};

// 页面加载时获取数据
onMounted(() => {
  fetchRoleList();
});
</script>

<style scoped>
.role-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>