<template>
  <div class="sale-list-page">
    <div class="page-header">
      <h2>销售管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加销售记录
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="saleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="medicineName" label="药品名称" min-width="120" />
      <el-table-column prop="customerName" label="客户" width="100" />
      <el-table-column prop="quantity" label="数量" width="80" />
      <el-table-column prop="price" label="单价" width="100">
        <template #default="{ row }">
          ¥{{ row.price?.toFixed(2) || '0.00' }}
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总金额" width="100">
        <template #default="{ row }">
          ¥{{ ((row.quantity || 0) * (row.price || 0)).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="saleDate" label="销售日期" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button 
            v-if="row.status === 'COMPLETED'" 
            type="warning" 
            link 
            @click="handleRefund(row)"
          >
            退货
          </el-button>
          <el-popconfirm
            title="确定要删除这条数据吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 销售表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <SaleForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 退货对话框 -->
    <el-dialog
      v-model="refundDialogVisible"
      title="销售退货"
      width="40%"
      destroy-on-close
    >
      <el-form
        ref="refundFormRef"
        :model="refundForm"
        :rules="refundRules"
        label-width="100px"
      >
        <el-form-item label="退货数量" prop="refundQuantity">
          <el-input-number
            v-model="refundForm.refundQuantity"
            :min="1"
            :max="refundForm.maxQuantity"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="退货原因" prop="reason">
          <el-input
            v-model="refundForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入退货原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="refundDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmRefund">确认退货</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getSaleList, deleteSale, refundSale } from '@/api/modules/sale';
import { getAllCustomers } from '@/api/modules/customer';
import { getMedicineList } from '@/api/modules/medicine';
import type { Sale, Customer, Medicine, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import SaleForm from './SaleForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '药品名称', prop: 'medicineName', type: 'input' },
    { 
      label: '客户', 
      prop: 'customerId', 
      type: 'select',
      options: [] as { label: string; value: number }[]
    },
    { label: '销售日期', prop: 'saleDate', type: 'daterange' },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '已完成', value: 'COMPLETED' },
        { label: '已退货', value: 'REFUNDED' }
      ]
    }
  ]
});

// 数据列表
const saleList = ref<Sale[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加销售记录',
    'edit': '编辑销售记录',
    'view': '查看销售记录详情'
  };
  return titleMap[formMode.value];
});

// 退货对话框相关
const refundDialogVisible = ref(false);
const refundFormRef = ref<FormInstance>();
const refundForm = reactive({
  saleId: 0,
  refundQuantity: 1,
  maxQuantity: 1,
  reason: ''
});
const refundRules = reactive<FormRules>({
  refundQuantity: [
    { required: true, message: '请输入退货数量', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入退货原因', trigger: 'blur' }
  ]
});

// 获取客户列表
const fetchCustomers = async () => {
  try {
    const customerList = await getAllCustomers();
    searchConfig.fields[1].options = customerList.map(item => ({
      label: item.name,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取客户列表失败:', error);
  }
};

// 获取销售列表
const fetchSaleList = async () => {
  loading.value = true;
  try {
    const result = await getSaleList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    saleList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取销售列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchSaleList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchSaleList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchSaleList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Sale) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Sale) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteSale(id);
    ElMessage.success('删除成功');
    fetchSaleList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理退货
const handleRefund = (row: Sale) => {
  refundForm.saleId = row.id!;
  refundForm.refundQuantity = 1;
  refundForm.maxQuantity = row.quantity;
  refundForm.reason = '';
  refundDialogVisible.value = true;
};

// 确认退货
const handleConfirmRefund = async () => {
  if (!refundFormRef.value) return;
  
  await refundFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await refundSale(refundForm.saleId, {
          reason: refundForm.reason,
          refundQuantity: refundForm.refundQuantity
        });
        ElMessage.success('退货成功');
        refundDialogVisible.value = false;
        fetchSaleList();
      } catch (error: any) {
        ElMessage.error(error.msg || '退货失败');
      }
    }
  });
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchSaleList();
};

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': 'success',
    'REFUNDED': 'warning'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': '已完成',
    'REFUNDED': '已退货'
  };
  return statusMap[status] || status;
};

// 页面加载时获取数据
onMounted(() => {
  fetchCustomers();
  fetchSaleList();
});
</script>

<style scoped>
.sale-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>