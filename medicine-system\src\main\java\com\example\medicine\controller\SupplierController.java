package com.example.medicine.controller;

import com.example.medicine.common.Result;
import com.example.medicine.entity.Supplier;
import com.example.medicine.service.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/supplier")
public class SupplierController {
    @Autowired
    private SupplierService supplierService;

    @PostMapping("/add")
    public Result<Supplier> addSupplier(@RequestBody Supplier supplier) {
        try {
            return Result.success(supplierService.addSupplier(supplier));
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{id}")
    public Result<Supplier> updateSupplier(@PathVariable Long id, @RequestBody Supplier supplier) {
        try {
            return Result.success(supplierService.updateSupplier(id, supplier));
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteSupplier(@PathVariable Long id) {
        try {
            supplierService.deleteSupplier(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/get/{id}")
    public Result<Supplier> getSupplierById(@PathVariable Long id) {
        try {
            return Result.success(supplierService.getSupplierById(id));
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    public Result<List<Supplier>> getAllSuppliers() {
        try {
            return Result.success(supplierService.getAllSuppliers());
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public Result<List<Supplier>> getAllSuppliersForSelect() {
        try {
            return Result.success(supplierService.getAllSuppliers());
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}