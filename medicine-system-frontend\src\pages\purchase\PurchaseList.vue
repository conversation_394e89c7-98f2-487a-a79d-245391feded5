<template>
  <div class="purchase-list-page">
    <div class="page-header">
      <h2>采购管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加采购记录
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="purchaseList"
      border
      style="width: 100%"
    >
      <el-table-column label="药品名称" min-width="120">
        <template #default="{ row }">
          {{ row.medicine?.name || '未知药品' }}
        </template>
      </el-table-column>
      <el-table-column label="供应商" width="120">
        <template #default="{ row }">
          {{ row.supplier?.name || '未知供应商' }}
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="数量" width="80" />
      <el-table-column prop="price" label="单价" width="100">
        <template #default="{ row }">
          ¥{{ row.price?.toFixed(2) || '0.00' }}
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总金额" width="100">
        <template #default="{ row }">
          ¥{{ ((row.quantity || 0) * (row.price || 0)).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="采购日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.purchaseDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button 
            v-if="row.status === 'PENDING'" 
            type="success" 
            link 
            @click="handleApprove(row)"
          >
            审批
          </el-button>
          <el-button 
            v-if="row.status === 'APPROVED'" 
            type="warning" 
            link 
            @click="handleComplete(row)"
          >
            完成
          </el-button>
          <el-popconfirm
            title="确定要删除这条数据吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 采购表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <PurchaseForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 审批对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="采购审批"
      width="40%"
      destroy-on-close
    >
      <el-form
        ref="approveFormRef"
        :model="approveForm"
        :rules="approveRules"
        label-width="100px"
      >
        <el-form-item label="审批结果" prop="approved">
          <el-radio-group v-model="approveForm.approved">
            <el-radio :label="true">通过</el-radio>
            <el-radio :label="false">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批备注" prop="remark">
          <el-input
            v-model="approveForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入审批备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmApprove">确认审批</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getPurchaseList, deletePurchase, approvePurchase, completePurchase } from '@/api/modules/purchase';
import { getAllSuppliers } from '@/api/modules/supplier';
import type { Purchase, Supplier, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import PurchaseForm from './PurchaseForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '药品名称', prop: 'medicineName', type: 'input' },
    { 
      label: '供应商', 
      prop: 'supplierId', 
      type: 'select',
      options: [] as { label: string; value: number }[]
    },
    { label: '采购日期', prop: 'purchaseDate', type: 'daterange' },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '待审批', value: 'PENDING' },
        { label: '已审批', value: 'APPROVED' },
        { label: '已完成', value: 'COMPLETED' },
        { label: '已取消', value: 'CANCELLED' }
      ]
    }
  ]
});

// 数据列表
const purchaseList = ref<Purchase[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加采购记录',
    'edit': '编辑采购记录',
    'view': '查看采购记录详情'
  };
  return titleMap[formMode.value];
});

// 审批对话框相关
const approveDialogVisible = ref(false);
const approveFormRef = ref<FormInstance>();
const approveForm = reactive({
  purchaseId: 0,
  approved: true,
  remark: ''
});
const approveRules = reactive<FormRules>({
  approved: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ]
});

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    const supplierList = await getAllSuppliers();
    searchConfig.fields[1].options = supplierList.map(item => ({
      label: item.name,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取供应商列表失败:', error);
  }
};

// 获取采购列表
const fetchPurchaseList = async () => {
  loading.value = true;
  try {
    const result = await getPurchaseList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    purchaseList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取采购列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchPurchaseList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchPurchaseList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchPurchaseList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Purchase) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Purchase) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deletePurchase(id);
    ElMessage.success('删除成功');
    fetchPurchaseList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理审批
const handleApprove = (row: Purchase) => {
  approveForm.purchaseId = row.id!;
  approveForm.approved = true;
  approveForm.remark = '';
  approveDialogVisible.value = true;
};

// 确认审批
const handleConfirmApprove = async () => {
  if (!approveFormRef.value) return;
  
  await approveFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await approvePurchase(approveForm.purchaseId, {
          approved: approveForm.approved,
          remark: approveForm.remark
        });
        ElMessage.success('审批成功');
        approveDialogVisible.value = false;
        fetchPurchaseList();
      } catch (error: any) {
        ElMessage.error(error.msg || '审批失败');
      }
    }
  });
};

// 处理完成
const handleComplete = async (row: Purchase) => {
  try {
    await ElMessageBox.confirm('确定要完成这个采购订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await completePurchase(row.id!);
    ElMessage.success('操作成功');
    fetchPurchaseList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '操作失败');
    }
  }
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchPurchaseList();
};

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': 'warning',
    'APPROVED': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待审批',
    'APPROVED': '已审批',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  };
  return statusMap[status] || status;
};

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 页面加载时获取数据
onMounted(() => {
  fetchSuppliers();
  fetchPurchaseList();
});
</script>

<style scoped>
.purchase-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>