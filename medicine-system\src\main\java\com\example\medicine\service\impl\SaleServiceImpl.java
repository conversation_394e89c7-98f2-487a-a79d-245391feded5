package com.example.medicine.service.impl;

import com.example.medicine.entity.Sale;
import com.example.medicine.repository.SaleRepository;
import com.example.medicine.service.SaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SaleServiceImpl implements SaleService {

    @Autowired
    private SaleRepository saleRepository;

    @Override
    public List<Sale> findAll() {
        return saleRepository.findAll();
    }

    @Override
    public Sale findById(Long id) {
        return saleRepository.findById(id).orElse(null);
    }

    @Override
    public Sale save(Sale sale) {
        return saleRepository.save(sale);
    }

    @Override
    public void deleteById(Long id) {
        saleRepository.deleteById(id);
    }

    @Override
    public Sale refundSale(Long id, String reason, Integer refundQuantity) {
        Sale sale = findById(id);
        if (sale == null) {
            throw new RuntimeException("销售记录不存在");
        }

        if (!"COMPLETED".equals(sale.getStatus())) {
            throw new RuntimeException("只有已完成的销售记录才能退货");
        }

        if (refundQuantity <= 0 || refundQuantity > sale.getQuantity()) {
            throw new RuntimeException("退货数量无效");
        }

        // 更新销售记录状态为已退货
        sale.setStatus("REFUNDED");

        // 这里可以添加更多退货逻辑，比如：
        // 1. 记录退货原因和数量到退货表
        // 2. 更新库存
        // 3. 记录操作日志

        return saleRepository.save(sale);
    }
}