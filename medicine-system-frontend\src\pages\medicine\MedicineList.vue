<template>
  <div class="medicine-list-page">
    <div class="page-header">
      <h2>药品管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加药品
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="medicineList"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="药品名称" min-width="120" />
      <el-table-column prop="categoryName" label="分类" width="100" />
      <el-table-column prop="spec" label="规格" width="100" />
      <el-table-column prop="batchNo" label="批号" width="120" />
      <el-table-column prop="expireDate" label="有效期" width="120" />
      <el-table-column prop="price" label="单价" width="100">
        <template #default="{ row }">
          ¥{{ row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="stock" label="库存量" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-popconfirm
            title="确定要删除这条数据吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 药品表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <MedicineForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getMedicineList, deleteMedicine, getMedicineCategoryList } from '@/api/modules/medicine';
import type { Medicine, MedicineCategory, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import MedicineForm from './MedicineForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '药品名称', prop: 'name', type: 'input' },
    { 
      label: '分类', 
      prop: 'categoryId', 
      type: 'select',
      options: [] as { label: string; value: number }[]
    },
    { label: '规格', prop: 'spec', type: 'input' },
    { label: '批号', prop: 'batchNo', type: 'input' },
    { label: '有效期', prop: 'expireDate', type: 'date' },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  ]
});

// 数据列表
const medicineList = ref<Medicine[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加药品',
    'edit': '编辑药品',
    'view': '查看药品详情'
  };
  return titleMap[formMode.value];
});

// 获取药品分类列表
const fetchCategories = async () => {
  try {
    const categoryList = await getMedicineCategoryList();
    searchConfig.fields[1].options = categoryList.map(item => ({
      label: item.name,
      value: item.id
    }));
  } catch (error) {
    console.error('获取药品分类失败:', error);
  }
};

// 获取药品列表
const fetchMedicineList = async () => {
  loading.value = true;
  try {
    const result = await getMedicineList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    medicineList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取药品列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  // 更新搜索参数
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1; // 重置到第一页
  fetchMedicineList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchMedicineList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchMedicineList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Medicine) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Medicine) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteMedicine(id);
    ElMessage.success('删除成功');
    fetchMedicineList(); // 重新加载列表
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchMedicineList(); // 重新加载列表
};

// 页面加载时获取数据
onMounted(() => {
  fetchCategories();
  fetchMedicineList();
});
</script>

<style scoped>
.medicine-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
