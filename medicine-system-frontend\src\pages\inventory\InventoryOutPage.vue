<template>
  <div class="inventory-out-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>出库管理</h2>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button type="danger" @click="showQuickOutDialog">
          <el-icon><Minus /></el-icon>快速出库
        </el-button>
      </div>
    </div>

    <!-- 统计信息面板 -->
    <div class="stats-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ todayOutCount }}</div>
              <div class="stats-label">今日出库</div>
            </div>
            <el-icon class="stats-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ monthOutCount }}</div>
              <div class="stats-label">本月出库</div>
            </div>
            <el-icon class="stats-icon"><DataAnalysis /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ todayOutQuantity }}</div>
              <div class="stats-label">今日出库数量</div>
            </div>
            <el-icon class="stats-icon"><Box /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ recentMedicineCount }}</div>
              <div class="stats-label">涉及药品种类</div>
            </div>
            <el-icon class="stats-icon"><Document /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选区域 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 出库记录列表 -->
    <div class="records-section">
      <div class="section-header">
        <h3>出库记录</h3>
        <div class="section-actions">
          <el-button size="small" @click="exportRecords">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="recordsList"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="记录ID" width="80" />
        <el-table-column prop="medicineName" label="药品名称" min-width="150" />
        <el-table-column prop="quantity" label="出库数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="danger">-{{ row.quantity }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="beforeStock" label="出库前库存" width="120" align="center" />
        <el-table-column prop="afterStock" label="出库后库存" width="120" align="center" />
        <el-table-column prop="reason" label="出库原因" min-width="150" show-overflow-tooltip />
        <el-table-column prop="operatorName" label="操作人" width="100" />
        <el-table-column prop="createTime" label="出库时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="viewRecordDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <!-- 快速出库对话框 -->
    <el-dialog
      v-model="quickOutDialogVisible"
      title="快速出库"
      width="50%"
      destroy-on-close
    >
      <InventoryForm
        type="out"
        @success="handleOutSuccess"
        @cancel="quickOutDialogVisible = false"
      />
    </el-dialog>

    <!-- 记录详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="出库记录详情"
      width="60%"
      destroy-on-close
    >
      <div v-if="selectedRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">{{ selectedRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="药品名称">{{ selectedRecord.medicineName }}</el-descriptions-item>
          <el-descriptions-item label="出库数量">
            <el-tag type="danger">-{{ selectedRecord.quantity }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="出库前库存">{{ selectedRecord.beforeStock }}</el-descriptions-item>
          <el-descriptions-item label="出库后库存">{{ selectedRecord.afterStock }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ selectedRecord.operatorName }}</el-descriptions-item>
          <el-descriptions-item label="出库时间" :span="2">
            {{ formatDateTime(selectedRecord.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="出库原因" :span="2">
            {{ selectedRecord.reason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Refresh, Minus, Download, TrendCharts, DataAnalysis,
  Box, Document
} from '@element-plus/icons-vue';
import type { InventoryRecord } from '@/types';
import { getInventoryRecords } from '@/api/modules/inventory';
import SearchForm from '@/components/common/SearchForm.vue';
import InventoryForm from './InventoryForm.vue';

// 响应式数据
const loading = ref(false);
const recordsList = ref<InventoryRecord[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 对话框状态
const quickOutDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const selectedRecord = ref<InventoryRecord | null>(null);

// 统计数据
const todayOutCount = ref(0);
const monthOutCount = ref(0);
const todayOutQuantity = ref(0);
const recentMedicineCount = ref(0);

// 搜索参数
const searchParams = reactive({
  medicineName: '',
  dateRange: null as any,
  operator: ''
});

// 搜索配置
const searchConfig = reactive({
  fields: [
    {
      type: 'input',
      prop: 'medicineName',
      label: '药品名称',
      placeholder: '请输入药品名称'
    },
    {
      type: 'daterange',
      prop: 'dateRange',
      label: '出库时间',
      placeholder: ['开始日期', '结束日期']
    },
    {
      type: 'input',
      prop: 'operator',
      label: '操作人',
      placeholder: '请输入操作人姓名'
    }
  ]
});

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 获取出库记录列表
const fetchRecordsList = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      type: 'OUT', // 只获取出库记录
      ...searchParams
    };

    const response = await getInventoryRecords(params);
    recordsList.value = response.records || [];
    total.value = response.total || 0;

    // 计算统计数据
    calculateStats();
  } catch (error: any) {
    console.error('获取出库记录失败:', error);
    ElMessage.error('获取出库记录失败');
  } finally {
    loading.value = false;
  }
};

// 计算统计数据
const calculateStats = () => {
  const today = new Date().toDateString();
  const thisMonth = new Date().getMonth();
  const thisYear = new Date().getFullYear();

  const todayRecords = recordsList.value.filter(record =>
    new Date(record.createTime || '').toDateString() === today
  );

  const monthRecords = recordsList.value.filter(record => {
    const recordDate = new Date(record.createTime || '');
    return recordDate.getMonth() === thisMonth && recordDate.getFullYear() === thisYear;
  });

  todayOutCount.value = todayRecords.length;
  monthOutCount.value = monthRecords.length;
  todayOutQuantity.value = todayRecords.reduce((sum, record) => sum + (record.quantity || 0), 0);

  // 计算涉及的药品种类数
  const uniqueMedicines = new Set(recordsList.value.map(record => record.medicineId));
  recentMedicineCount.value = uniqueMedicines.size;
};

// 搜索处理
const handleSearch = (params: any) => {
  Object.assign(searchParams, params);
  currentPage.value = 1;
  fetchRecordsList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams, {
    medicineName: '',
    dateRange: null,
    operator: ''
  });
  currentPage.value = 1;
  fetchRecordsList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchRecordsList();
});

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  // 这里可以添加排序逻辑
  console.log('排序:', prop, order);
};

// 显示快速出库对话框
const showQuickOutDialog = () => {
  quickOutDialogVisible.value = true;
};

// 出库成功处理
const handleOutSuccess = () => {
  quickOutDialogVisible.value = false;
  ElMessage.success('出库成功！');
  fetchRecordsList();
};

// 查看记录详情
const viewRecordDetail = (record: InventoryRecord) => {
  selectedRecord.value = record;
  detailDialogVisible.value = true;
};

// 导出记录
const exportRecords = () => {
  ElMessage.info('导出功能开发中...');
};

// 刷新数据
const refreshData = () => {
  fetchRecordsList();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchRecordsList();
});
</script>

<style scoped>
.inventory-out-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-panel {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #f56c6c;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.stats-icon {
  font-size: 40px;
  color: #fef0f0;
  opacity: 0.8;
}

.records-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.record-detail {
  padding: 20px 0;
}
</style>
