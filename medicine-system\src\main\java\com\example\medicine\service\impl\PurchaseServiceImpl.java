package com.example.medicine.service.impl;

import com.example.medicine.entity.Purchase;
import com.example.medicine.repository.PurchaseRepository;
import com.example.medicine.service.PurchaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PurchaseServiceImpl implements PurchaseService {

    @Autowired
    private PurchaseRepository purchaseRepository;

    @Override
    public List<Purchase> findAll() {
        return purchaseRepository.findAll();
    }

    @Override
    public List<Purchase> findAllWithDetails() {
        return purchaseRepository.findAllWithDetails();
    }

    @Override
    public Purchase findById(Long id) {
        return purchaseRepository.findById(id).orElse(null);
    }

    @Override
    public Purchase findByIdWithDetails(Long id) {
        return purchaseRepository.findByIdWithDetails(id);
    }

    @Override
    public Purchase save(Purchase purchase) {
        return purchaseRepository.save(purchase);
    }

    @Override
    public void deleteById(Long id) {
        purchaseRepository.deleteById(id);
    }

    @Override
    public Purchase completePurchase(Long id) {
        Purchase purchase = findByIdWithDetails(id);
        if (purchase == null) {
            throw new RuntimeException("采购记录不存在");
        }
        if (!"APPROVED".equals(purchase.getStatus())) {
            throw new RuntimeException("只有已审批的采购记录才能完成");
        }
        purchase.setStatus("COMPLETED");
        return purchaseRepository.save(purchase);
    }
}