package com.example.medicine.repository;

import com.example.medicine.entity.Purchase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PurchaseRepository extends JpaRepository<Purchase, Long> {

    @Query("SELECT p FROM Purchase p LEFT JOIN FETCH p.supplier LEFT JOIN FETCH p.medicine")
    List<Purchase> findAllWithDetails();

    @Query("SELECT p FROM Purchase p LEFT JOIN FETCH p.supplier LEFT JOIN FETCH p.medicine WHERE p.id = :id")
    Purchase findByIdWithDetails(@Param("id") Long id);
}