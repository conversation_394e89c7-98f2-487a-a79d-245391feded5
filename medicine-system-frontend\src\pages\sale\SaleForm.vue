<template>
  <div class="sale-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="客户" prop="customerId">
        <el-select
          v-model="formData.customerId"
          filterable
          placeholder="请选择客户"
          style="width: 100%"
        >
          <el-option
            v-for="item in customerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="药品" prop="medicineId">
        <el-select
          v-model="formData.medicineId"
          filterable
          placeholder="请选择药品"
          style="width: 100%"
          @change="handleMedicineChange"
        >
          <el-option
            v-for="item in medicineOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="数量" prop="quantity">
        <el-input-number
          v-model="formData.quantity"
          :min="1"
          :max="selectedMedicine?.stock || 999"
          :precision="0"
          style="width: 100%"
          @change="calculateTotal"
        />
        <div v-if="selectedMedicine" class="stock-info">
          库存：{{ selectedMedicine.stock }}
        </div>
      </el-form-item>
      
      <el-form-item label="单价" prop="price">
        <el-input-number
          v-model="formData.price"
          :precision="2"
          :step="0.1"
          :min="0"
          style="width: 100%"
          @change="calculateTotal"
        />
      </el-form-item>
      
      <el-form-item label="总金额">
        <el-input
          :value="totalAmount.toFixed(2)"
          readonly
          style="width: 100%"
        >
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="销售日期" prop="saleDate">
        <el-date-picker
          v-model="formData.saleDate"
          type="datetime"
          placeholder="请选择销售日期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="COMPLETED">已完成</el-radio>
          <el-radio label="REFUNDED">已退货</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">{{ mode === 'view' ? '返回' : '取消' }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getSaleDetail, addSale, updateSale } from '@/api/modules/sale';
import { getCustomerList } from '@/api/modules/customer';
import { getMedicineList } from '@/api/modules/medicine';
import type { Sale, Customer, Medicine } from '@/types';

// 定义组件的props
const props = defineProps<{
  id?: number;
  mode?: 'add' | 'edit' | 'view';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<Sale>({
  customerId: undefined,
  medicineId: undefined,
  quantity: 1,
  price: 0,
  saleDate: new Date().toISOString(),
  status: 'COMPLETED'
});

// 表单验证规则
const rules = reactive<FormRules>({
  customerId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  medicineId: [
    { required: true, message: '请选择药品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  saleDate: [
    { required: true, message: '请选择销售日期', trigger: 'change' }
  ]
});

// 选项数据
const customerOptions = ref<{ label: string; value: number }[]>([]);
const medicineOptions = ref<{ label: string; value: number }[]>([]);
const medicineList = ref<Medicine[]>([]);

// 计算属性
const selectedMedicine = computed(() => {
  return medicineList.value.find(item => item.id === formData.medicineId);
});

const totalAmount = computed(() => {
  return (formData.quantity || 0) * (formData.price || 0);
});

// 获取客户列表
const fetchCustomers = async () => {
  try {
    const result = await getCustomerList({ page: 1, size: 100 });
    const customerList = Array.isArray(result) ? result : result.records || [];
    customerOptions.value = customerList.map(item => ({
      label: item.name,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取客户列表失败:', error);
  }
};

// 获取药品列表
const fetchMedicines = async () => {
  try {
    const result = await getMedicineList({ page: 1, size: 1000 });
    medicineList.value = result.records;
    medicineOptions.value = result.records.map(item => ({
      label: `${item.name} (${item.spec})`,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取药品列表失败:', error);
  }
};

// 获取销售详情
const fetchSaleDetail = async (id: number) => {
  try {
    const detail = await getSaleDetail(id);
    Object.keys(detail).forEach(key => {
      if (key in formData) {
        formData[key as keyof Sale] = detail[key as keyof Sale];
      }
    });
  } catch (error: any) {
    ElMessage.error(error.msg || '获取销售详情失败');
  }
};

// 处理药品选择变化
const handleMedicineChange = (medicineId: number) => {
  const medicine = medicineList.value.find(item => item.id === medicineId);
  if (medicine) {
    formData.price = medicine.price;
    calculateTotal();
  }
};

// 计算总金额
const calculateTotal = () => {
  // 总金额通过计算属性自动计算
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 检查库存
        if (selectedMedicine.value && formData.quantity > selectedMedicine.value.stock) {
          ElMessage.error('销售数量不能超过库存数量');
          return;
        }

        const submitData = {
          ...formData,
          totalAmount: totalAmount.value
        };

        if (props.mode === 'edit' && props.id) {
          await updateSale(props.id, submitData);
          ElMessage.success('更新成功');
        } else {
          await addSale(submitData);
          ElMessage.success('添加成功');
        }
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.msg || '操作失败');
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchCustomers();
  await fetchMedicines();
  
  if ((props.mode === 'edit' || props.mode === 'view') && props.id) {
    await fetchSaleDetail(props.id);
  }
});
</script>

<style scoped>
.sale-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stock-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>