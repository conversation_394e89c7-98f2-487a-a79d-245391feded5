# 采购添加页面功能说明

## 🎯 功能概述
新增的采购添加页面 (`/purchase/add`) 提供了完整的采购记录创建功能，具有现代化的用户界面和完善的表单验证。

## 📍 访问地址
- **开发环境**: http://localhost:3001/purchase/add
- **生产环境**: http://localhost:3000/purchase/add

## ✨ 主要功能

### 1. 页面布局
- **面包屑导航**: 采购管理 > 添加采购记录
- **页面标题**: 带图标的醒目标题
- **描述信息**: 清晰的操作指引
- **卡片式表单**: 现代化的表单容器

### 2. 表单字段
- **供应商选择**: 下拉选择，支持搜索过滤
- **药品选择**: 下拉选择，显示药品名称和规格
- **采购数量**: 数字输入，最小值为1
- **单价**: 数字输入，支持小数点后2位
- **总金额**: 自动计算，只读显示
- **采购日期**: 日期时间选择器
- **状态**: 单选按钮组（待审批/已审批/已完成/已取消）

### 3. 智能功能
- **自动计算**: 选择药品后自动填入单价
- **实时计算**: 数量或单价变化时自动计算总金额
- **表单验证**: 完整的前端验证规则
- **加载状态**: 提交时显示加载动画

### 4. 用户体验
- **响应式设计**: 适配桌面和移动设备
- **暗黑模式**: 支持主题切换
- **平滑动画**: 悬停和交互动效
- **错误处理**: 友好的错误提示

## 🔧 技术实现

### 路由配置
```typescript
{
  path: '/purchase/add',
  name: 'PurchaseAdd',
  component: () => import('../pages/purchase/PurchaseAddPage.vue'),
  meta: { requiresAuth: true, permission: 'purchase:add' }
}
```

### 组件结构
- `PurchaseAddPage.vue`: 主页面组件
- `PurchaseForm.vue`: 复用的表单组件
- 权限控制: `purchase:add` 权限

### API接口
- `POST /purchase/add`: 创建采购记录
- `GET /supplier/all`: 获取供应商列表
- `GET /medicine/list`: 获取药品列表

## 🎨 设计特色

### 视觉设计
- **毛玻璃效果**: 现代化的卡片设计
- **渐变背景**: 精美的头部渐变
- **阴影效果**: 立体感的卡片阴影
- **图标系统**: Element Plus 图标库

### 交互设计
- **悬停效果**: 按钮和卡片的悬停动画
- **焦点状态**: 输入框的焦点高亮
- **加载反馈**: 提交时的加载状态
- **成功反馈**: 操作成功的消息提示

## 🚀 使用流程

1. **访问页面**: 点击采购管理中的"添加采购记录"按钮
2. **填写信息**: 
   - 选择供应商
   - 选择药品（自动填入单价）
   - 输入采购数量
   - 选择采购日期
   - 设置状态
3. **确认提交**: 点击"创建采购记录"按钮
4. **完成操作**: 成功后自动跳转回采购列表

## 📱 响应式支持

### 桌面端 (>768px)
- 双列布局
- 侧边操作按钮
- 完整的表单标签

### 移动端 (≤768px)
- 单列布局
- 全宽按钮
- 紧凑的间距

## 🔒 权限控制
- 需要登录认证
- 需要 `purchase:add` 权限
- 自动权限验证和重定向

## 🎯 后续优化建议
1. 添加批量导入功能
2. 支持采购模板保存
3. 集成条码扫描
4. 添加采购预算控制
5. 支持多币种采购
