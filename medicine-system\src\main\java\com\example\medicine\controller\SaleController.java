package com.example.medicine.controller;

import com.example.medicine.entity.Sale;
import com.example.medicine.service.SaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/sale")
public class SaleController {

    @Autowired
    private SaleService saleService;

    @GetMapping("/list")
    public List<Sale> list() {
        return saleService.findAll();
    }

    @GetMapping("/{id}")
    public Sale get(@PathVariable Long id) {
        return saleService.findById(id);
    }

    @PostMapping("/add")
    public Sale add(@RequestBody Sale sale) {
        return saleService.save(sale);
    }

    @PutMapping("/update/{id}")
    public Sale update(@PathVariable Long id, @RequestBody Sale sale) {
        sale.setId(id);
        return saleService.save(sale);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        saleService.deleteById(id);
    }

    @PostMapping("/refund/{id}")
    public Sale refund(@PathVariable Long id, @RequestBody Map<String, Object> refundData) {
        String reason = (String) refundData.get("reason");
        Integer refundQuantity = (Integer) refundData.get("refundQuantity");
        return saleService.refundSale(id, reason, refundQuantity);
    }
}